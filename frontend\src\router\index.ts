import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue'),
    meta: { requiresAuth: true, title: '首页' }
  },
  {
    path: '/personnel',
    name: 'Personnel',
    component: () => import('@/views/personnel/PersonnelList.vue'),
    meta: { requiresAuth: true, title: '人员管理' }
  },
  {
    path: '/personnel/create',
    name: 'PersonnelCreate',
    component: () => import('@/views/personnel/PersonnelForm.vue'),
    meta: { requiresAuth: true, title: '新增人员' }
  },
  {
    path: '/personnel/:id',
    name: 'PersonnelDetail',
    component: () => import('@/views/personnel/PersonnelDetail.vue'),
    meta: { requiresAuth: true, title: '人员详情' }
  },
  {
    path: '/personnel/:id/edit',
    name: 'PersonnelEdit',
    component: () => import('@/views/personnel/PersonnelForm.vue'),
    meta: { requiresAuth: true, title: '编辑人员' }
  },
  {
    path: '/applications',
    name: 'Applications',
    component: () => import('@/views/applications/ApplicationList.vue'),
    meta: { requiresAuth: true, title: '应用管理' }
  },
  {
    path: '/applications/create',
    name: 'ApplicationCreate',
    component: () => import('@/views/applications/ApplicationForm.vue'),
    meta: { requiresAuth: true, title: '新增应用' }
  },
  {
    path: '/applications/:id',
    name: 'ApplicationDetail',
    component: () => import('@/views/applications/ApplicationDetail.vue'),
    meta: { requiresAuth: true, title: '应用详情' }
  },
  {
    path: '/applications/:id/edit',
    name: 'ApplicationEdit',
    component: () => import('@/views/applications/ApplicationForm.vue'),
    meta: { requiresAuth: true, title: '编辑应用' }
  },
  {
    path: '/open-platform',
    name: 'OpenPlatform',
    component: () => import('@/views/open-platform/OpenPlatformList.vue'),
    meta: { requiresAuth: true, title: '开放平台主体' }
  },
  {
    path: '/open-platform/create',
    name: 'OpenPlatformCreate',
    component: () => import('@/views/open-platform/OpenPlatformForm.vue'),
    meta: { requiresAuth: true, title: '新增主体' }
  },
  {
    path: '/open-platform/:id',
    name: 'OpenPlatformDetail',
    component: () => import('@/views/open-platform/OpenPlatformDetail.vue'),
    meta: { requiresAuth: true, title: '主体详情' }
  },
  {
    path: '/open-platform/:id/edit',
    name: 'OpenPlatformEdit',
    component: () => import('@/views/open-platform/OpenPlatformForm.vue'),
    meta: { requiresAuth: true, title: '编辑主体' }
  },
  {
    path: '/audit-logs',
    name: 'AuditLogs',
    component: () => import('@/views/audit/AuditLogs.vue'),
    meta: { requiresAuth: true, title: '变更记录' }
  },
  {
    path: '/data-management',
    name: 'DataManagement',
    component: () => import('@/views/data/DataManagement.vue'),
    meta: { requiresAuth: true, title: '数据管理' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/Profile.vue'),
    meta: { requiresAuth: true, title: '个人信息' }
  },
  {
    path: '/domains',
    name: 'Domains',
    component: () => import('@/views/domains/DomainList.vue'),
    meta: { requiresAuth: true, title: '域名备案登记' }
  },
  {
    path: '/domains/create',
    name: 'DomainCreate',
    component: () => import('@/views/domains/DomainForm.vue'),
    meta: { requiresAuth: true, title: '新增域名' }
  },
  {
    path: '/domains/:id',
    name: 'DomainDetail',
    component: () => import('@/views/domains/DomainDetail.vue'),
    meta: { requiresAuth: true, title: '域名详情' }
  },
  {
    path: '/domains/:id/edit',
    name: 'DomainEdit',
    component: () => import('@/views/domains/DomainForm.vue'),
    meta: { requiresAuth: true, title: '编辑域名' }
  },
  {
    path: '/domain-monitoring',
    name: 'DomainMonitoring',
    component: () => import('@/views/domain-monitoring/MonitoringList.vue'),
    meta: { requiresAuth: true, title: '域名过期检测' }
  },
  {
    path: '/domain-monitoring/config/:domainId',
    name: 'MonitoringConfig',
    component: () => import('@/views/domain-monitoring/MonitoringConfig.vue'),
    meta: { requiresAuth: true, title: '监控配置' }
  },
  {
    path: '/domain-monitoring/alerts',
    name: 'AlertHistory',
    component: () => import('@/views/domain-monitoring/AlertHistory.vue'),
    meta: { requiresAuth: true, title: '预警历史' }
  },
  // 手机管理模块路由
  {
    path: '/wechat-entities',
    name: 'WechatEntities',
    component: () => import('@/views/wechat-entities/WechatEntityList.vue'),
    meta: { requiresAuth: true, title: '企业微信主体' }
  },
  {
    path: '/wechat-entities/create',
    name: 'WechatEntityCreate',
    component: () => import('@/views/wechat-entities/WechatEntityForm.vue'),
    meta: { requiresAuth: true, title: '新增主体' }
  },
  {
    path: '/wechat-entities/:id',
    name: 'WechatEntityDetail',
    component: () => import('@/views/wechat-entities/WechatEntityDetail.vue'),
    meta: { requiresAuth: true, title: '主体详情' }
  },
  {
    path: '/wechat-entities/:id/edit',
    name: 'WechatEntityEdit',
    component: () => import('@/views/wechat-entities/WechatEntityForm.vue'),
    meta: { requiresAuth: true, title: '编辑主体' }
  },
  {
    path: '/devices',
    name: 'Devices',
    component: () => import('@/views/devices/DeviceList.vue'),
    meta: { requiresAuth: true, title: '设备管理' }
  },
  {
    path: '/devices/create',
    name: 'DeviceCreate',
    component: () => import('@/views/devices/DeviceForm.vue'),
    meta: { requiresAuth: true, title: '新增设备' }
  },
  {
    path: '/devices/:id',
    name: 'DeviceDetail',
    component: () => import('@/views/devices/DeviceDetail.vue'),
    meta: { requiresAuth: true, title: '设备详情' }
  },
  {
    path: '/devices/:id/edit',
    name: 'DeviceEdit',
    component: () => import('@/views/devices/DeviceForm.vue'),
    meta: { requiresAuth: true, title: '编辑设备' }
  },
  {
    path: '/phones',
    name: 'Phones',
    component: () => import('@/views/phones/PhoneList.vue'),
    meta: { requiresAuth: true, title: '号码管理' }
  },
  {
    path: '/phones/create',
    name: 'PhoneCreate',
    component: () => import('@/views/phones/PhoneForm.vue'),
    meta: { requiresAuth: true, title: '新增号码' }
  },
  {
    path: '/phones/:id',
    name: 'PhoneDetail',
    component: () => import('@/views/phones/PhoneDetail.vue'),
    meta: { requiresAuth: true, title: '号码详情' }
  },
  {
    path: '/phones/:id/edit',
    name: 'PhoneEdit',
    component: () => import('@/views/phones/PhoneForm.vue'),
    meta: { requiresAuth: true, title: '编辑号码' }
  },
  {
    path: '/device-purchase',
    name: 'DevicePurchaseList',
    component: () => import('@/views/device-purchase/DevicePurchaseList.vue'),
    meta: { requiresAuth: true, title: '采购记录' }
  },
  {
    path: '/device-purchase/create',
    name: 'DevicePurchaseCreate',
    component: () => import('@/views/device-purchase/DevicePurchaseForm.vue'),
    meta: { requiresAuth: true, title: '新增采购记录' }
  },
  {
    path: '/device-purchase/:id',
    name: 'DevicePurchaseDetail',
    component: () => import('@/views/device-purchase/DevicePurchaseDetail.vue'),
    meta: { requiresAuth: true, title: '采购记录详情' }
  },
  {
    path: '/device-purchase/:id/edit',
    name: 'DevicePurchaseEdit',
    component: () => import('@/views/device-purchase/DevicePurchaseForm.vue'),
    meta: { requiresAuth: true, title: '编辑采购记录' }
  },
  {
    path: '/device-maintenance',
    name: 'DeviceMaintenance',
    component: () => import('@/views/device-maintenance/DeviceMaintenanceList.vue'),
    meta: { requiresAuth: true, title: '维修记录' }
  },
  {
    path: '/device-maintenance/create',
    name: 'DeviceMaintenanceCreate',
    component: () => import('@/views/device-maintenance/DeviceMaintenanceForm.vue'),
    meta: { requiresAuth: true, title: '新增维修记录' }
  },
  {
    path: '/device-maintenance/:id',
    name: 'DeviceMaintenanceDetail',
    component: () => import('@/views/device-maintenance/DeviceMaintenanceDetail.vue'),
    meta: { requiresAuth: true, title: '维修记录详情' }
  },
  {
    path: '/device-maintenance/:id/edit',
    name: 'DeviceMaintenanceEdit',
    component: () => import('@/views/device-maintenance/DeviceMaintenanceForm.vue'),
    meta: { requiresAuth: true, title: '编辑维修记录' }
  },
  {
    path: '/phone-analytics',
    name: 'PhoneAnalytics',
    component: () => import('@/views/phone-analytics/PhoneAnalytics.vue'),
    meta: { requiresAuth: true, title: '统计分析' }
  },
  // 404页面 - 必须放在最后
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue'),
    meta: { title: '页面未找到' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 微信生态应用备案人员信息管理系统`
  }

  // 检查认证状态
  if (to.meta.requiresAuth) {
    const token = localStorage.getItem('token')
    if (!token) {
      next('/login')
      return
    }

    // 初始化用户信息到store（如果还没有初始化）
    const authStore = useAuthStore()
    if (!authStore.user && token) {
      authStore.checkAuth()
    }
  }

  next()
})

export default router