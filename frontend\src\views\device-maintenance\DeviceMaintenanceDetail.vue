<!-- 开发作者: 学文 -->
<!-- 开发时间: 2025-08-21 -->
<!-- 模块说明: 设备维修记录详情页面 -->

<template>
  <div class="device-maintenance-detail">
    <div class="page-header">
      <div class="header-left">
        <h1>维修记录详情</h1>
        <p>查看设备维修记录的详细信息</p>
      </div>
      <div class="header-right">
        <el-button @click="handleEdit" type="primary">
          <el-icon><Edit /></el-icon>
          编辑记录
        </el-button>
        <el-button @click="handleBack">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="detail-card">
      <template v-if="maintenanceRecord">
        <!-- 基本信息 -->
        <div class="info-section">
          <h3>基本信息</h3>
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <label>设备编号：</label>
                <span>{{ maintenanceRecord.device?.deviceCode || maintenanceRecord.deviceId }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>设备型号：</label>
                <span>{{ maintenanceRecord.device?.model || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>设备品牌：</label>
                <span>{{ maintenanceRecord.device?.brand || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="8">
              <div class="info-item">
                <label>维修类型：</label>
                <el-tag :type="maintenanceRecord.maintenanceType === 'warranty' ? 'success' : 'warning'">
                  {{ maintenanceRecord.maintenanceType === 'warranty' ? '保修' : '付费' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>维修状态：</label>
                <el-tag :type="getMaintenanceStatusType(maintenanceRecord.maintenanceStatus)">
                  {{ getMaintenanceStatusText(maintenanceRecord.maintenanceStatus) }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <label>维修费用：</label>
                <span class="cost">¥{{ maintenanceRecord.maintenanceCost || 0 }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 维修信息 -->
        <div class="info-section">
          <h3>维修信息</h3>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label>维修服务商：</label>
                <span>{{ maintenanceRecord.serviceProvider || '-' }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>维修技术员：</label>
                <span>{{ maintenanceRecord.technician || '-' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label>开始时间：</label>
                <span>{{ formatDateTime(maintenanceRecord.startTime) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>完成时间：</label>
                <span>{{ maintenanceRecord.endTime ? formatDateTime(maintenanceRecord.endTime) : '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </div>

        <!-- 故障描述 -->
        <div class="info-section">
          <h3>故障描述</h3>
          <div class="description-content">
            {{ maintenanceRecord.faultDescription }}
          </div>
        </div>

        <!-- 维修结果 -->
        <div class="info-section" v-if="maintenanceRecord.maintenanceResult">
          <h3>维修结果</h3>
          <div class="description-content">
            {{ maintenanceRecord.maintenanceResult }}
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="info-section">
          <h3>记录信息</h3>
          <el-row :gutter="24">
            <el-col :span="12">
              <div class="info-item">
                <label>创建时间：</label>
                <span>{{ formatDateTime(maintenanceRecord.createdAt) }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="info-item">
                <label>更新时间：</label>
                <span>{{ formatDateTime(maintenanceRecord.updatedAt) }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Edit, ArrowLeft } from '@element-plus/icons-vue'
import { DeviceMaintenanceApi, type DeviceMaintenanceRecord } from '@/api/deviceMaintenance'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const maintenanceRecord = ref<DeviceMaintenanceRecord | null>(null)

// 获取维修记录详情
const fetchDetail = async () => {
  try {
    loading.value = true
    const id = Number(route.params.id)
    
    if (!id) {
      ElMessage.error('无效的记录ID')
      router.push('/device-maintenance')
      return
    }

    const response = await DeviceMaintenanceApi.getMaintenanceRecordById(id)
    
    if (response.success) {
      maintenanceRecord.value = response.data
    } else {
      ElMessage.error(response.message || '获取详情失败')
      router.push('/device-maintenance')
    }
  } catch (error) {
    console.error('获取维修记录详情失败:', error)
    ElMessage.error('获取详情失败')
    router.push('/device-maintenance')
  } finally {
    loading.value = false
  }
}

// 编辑记录
const handleEdit = () => {
  if (maintenanceRecord.value) {
    router.push(`/device-maintenance/${maintenanceRecord.value.id}/edit`)
  }
}

// 返回列表
const handleBack = () => {
  router.push('/device-maintenance')
}

// 获取维修状态类型
const getMaintenanceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'in_progress': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

// 获取维修状态文本
const getMaintenanceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'in_progress': '维修中',
    'completed': '已完成',
    'failed': '维修失败'
  }
  return statusMap[status] || status
}

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDetail()
})
</script>

<style scoped>
.device-maintenance-detail {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.detail-card {
  min-height: 400px;
}

.info-section {
  margin-bottom: 32px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.info-item {
  margin-bottom: 16px;
}

.info-item label {
  display: inline-block;
  width: 100px;
  color: #606266;
  font-size: 14px;
}

.info-item span {
  color: #303133;
  font-size: 14px;
}

.cost {
  font-weight: 600;
  color: #f56c6c;
}

.description-content {
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
  color: #303133;
  font-size: 14px;
  line-height: 1.6;
  min-height: 60px;
}
</style>
