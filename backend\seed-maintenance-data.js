/**
 * 开发作者: 学文
 * 开发时间: 2025-08-21
 * 模块说明: 维修记录测试数据注入脚本
 */

const { sequelize } = require('./src/utils/database')
const { MobileDevice } = require('./src/models/MobileDevice')
const { DeviceMaintenanceRecord } = require('./src/models/DeviceMaintenanceRecord')
const { WechatEntity } = require('./src/models/WechatEntity')

// 维修记录测试数据
const maintenanceTestData = [
  {
    deviceCode: 'DEV001',
    faultDescription: '屏幕破裂，触摸失灵，无法正常使用',
    maintenanceType: 'warranty',
    startTime: '2024-01-15T09:00:00Z',
    endTime: '2024-01-16T15:00:00Z',
    maintenanceCost: 0,
    serviceProvider: '苹果官方维修中心',
    technician: '王师傅',
    maintenanceStatus: 'completed',
    maintenanceResult: '更换屏幕总成，功能恢复正常，已通过质检'
  },
  {
    deviceCode: 'DEV002',
    faultDescription: '电池老化，续航时间短，充电速度慢',
    maintenanceType: 'paid',
    startTime: '2024-01-20T10:00:00Z',
    endTime: null,
    maintenanceCost: 0,
    serviceProvider: '华为授权维修点',
    technician: '李师傅',
    maintenanceStatus: 'in_progress',
    maintenanceResult: ''
  },
  {
    deviceCode: 'DEV003',
    faultDescription: '系统卡顿，应用闪退，运行缓慢',
    maintenanceType: 'warranty',
    startTime: '2024-02-01T14:00:00Z',
    endTime: '2024-02-01T16:30:00Z',
    maintenanceCost: 0,
    serviceProvider: '小米官方服务中心',
    technician: '张技师',
    maintenanceStatus: 'completed',
    maintenanceResult: '重置系统，清理缓存，更新系统版本，性能恢复正常'
  },
  {
    deviceCode: 'DEV004',
    faultDescription: '摄像头模糊，拍照效果差，对焦困难',
    maintenanceType: 'paid',
    startTime: '2024-02-05T11:00:00Z',
    endTime: '2024-02-06T09:00:00Z',
    maintenanceCost: 280.00,
    serviceProvider: 'OPPO客户服务中心',
    technician: '刘工程师',
    maintenanceStatus: 'completed',
    maintenanceResult: '更换后置摄像头模组，清洁镜头，拍照功能恢复正常'
  },
  {
    deviceCode: 'DEV005',
    faultDescription: '充电接口松动，充电不稳定',
    maintenanceType: 'warranty',
    startTime: '2024-02-10T13:00:00Z',
    endTime: null,
    maintenanceCost: 0,
    serviceProvider: 'vivo官方维修站',
    technician: '陈师傅',
    maintenanceStatus: 'failed',
    maintenanceResult: '检测发现主板损坏，维修成本过高，建议更换设备'
  },
  {
    deviceCode: 'DEV006',
    faultDescription: '扬声器无声音，听筒声音小',
    maintenanceType: 'paid',
    startTime: '2024-02-15T15:30:00Z',
    endTime: '2024-02-15T17:00:00Z',
    maintenanceCost: 150.00,
    serviceProvider: '三星授权维修中心',
    technician: '赵技师',
    maintenanceStatus: 'completed',
    maintenanceResult: '更换扬声器组件，清理听筒灰尘，音频功能恢复正常'
  }
]

async function createTestDevices() {
  console.log('创建测试设备数据...')
  
  // 先创建企业微信主体
  const wechatEntities = [
    { name: '北京科技有限公司', entityType: 'enterprise', status: 'active' },
    { name: '上海创新科技公司', entityType: 'enterprise', status: 'active' },
    { name: '深圳智能科技', entityType: 'enterprise', status: 'active' }
  ]

  const createdEntities = []
  for (const entity of wechatEntities) {
    const existingEntity = await WechatEntity.findOne({ where: { name: entity.name } })
    if (!existingEntity) {
      const created = await WechatEntity.create(entity)
      createdEntities.push(created)
      console.log(`✅ 创建企业微信主体: ${entity.name}`)
    } else {
      createdEntities.push(existingEntity)
      console.log(`⚠️  企业微信主体已存在: ${entity.name}`)
    }
  }

  // 创建测试设备
  const testDevices = [
    { deviceCode: 'DEV001', model: 'iPhone 14', brand: 'Apple', status: 'maintenance', wechatEntityId: createdEntities[0].id },
    { deviceCode: 'DEV002', model: 'Mate 50', brand: 'Huawei', status: 'maintenance', wechatEntityId: createdEntities[0].id },
    { deviceCode: 'DEV003', model: 'Mi 13', brand: 'Xiaomi', status: 'in_use', wechatEntityId: createdEntities[1].id },
    { deviceCode: 'DEV004', model: 'Find X6', brand: 'OPPO', status: 'in_use', wechatEntityId: createdEntities[1].id },
    { deviceCode: 'DEV005', model: 'X90', brand: 'vivo', status: 'scrapped', wechatEntityId: createdEntities[2].id },
    { deviceCode: 'DEV006', model: 'Galaxy S23', brand: 'Samsung', status: 'in_use', wechatEntityId: createdEntities[2].id }
  ]

  const createdDevices = []
  for (const device of testDevices) {
    const existingDevice = await MobileDevice.findOne({ where: { deviceCode: device.deviceCode } })
    if (!existingDevice) {
      const created = await MobileDevice.create(device)
      createdDevices.push(created)
      console.log(`✅ 创建设备: ${device.deviceCode} - ${device.model}`)
    } else {
      createdDevices.push(existingDevice)
      console.log(`⚠️  设备已存在: ${device.deviceCode} - ${device.model}`)
    }
  }

  return createdDevices
}

async function seedMaintenanceData() {
  try {
    console.log('开始注入维修记录测试数据...')

    // 确保数据库连接
    await sequelize.authenticate()
    console.log('数据库连接成功')

    // 创建测试设备
    const devices = await createTestDevices()

    // 创建维修记录
    console.log('\n创建维修记录数据...')
    for (let i = 0; i < maintenanceTestData.length; i++) {
      const maintenanceData = maintenanceTestData[i]
      const device = devices.find(d => d.deviceCode === maintenanceData.deviceCode)
      
      if (!device) {
        console.log(`⚠️  未找到设备: ${maintenanceData.deviceCode}`)
        continue
      }

      const existingRecord = await DeviceMaintenanceRecord.findOne({
        where: {
          deviceId: device.id,
          startTime: maintenanceData.startTime
        }
      })

      if (!existingRecord) {
        await DeviceMaintenanceRecord.create({
          deviceId: device.id,
          faultDescription: maintenanceData.faultDescription,
          maintenanceType: maintenanceData.maintenanceType,
          startTime: maintenanceData.startTime,
          endTime: maintenanceData.endTime,
          maintenanceCost: maintenanceData.maintenanceCost,
          serviceProvider: maintenanceData.serviceProvider,
          technician: maintenanceData.technician,
          maintenanceStatus: maintenanceData.maintenanceStatus,
          maintenanceResult: maintenanceData.maintenanceResult
        })
        console.log(`✅ 创建维修记录: ${device.deviceCode} - ${maintenanceData.faultDescription.substring(0, 20)}...`)
      } else {
        console.log(`⚠️  维修记录已存在: ${device.deviceCode}`)
      }
    }

    console.log('\n🎉 维修记录测试数据注入完成！')
    console.log(`- 企业微信主体: 3 条`)
    console.log(`- 设备数据: 6 条`)
    console.log(`- 维修记录: ${maintenanceTestData.length} 条`)

  } catch (error) {
    console.error('❌ 维修记录测试数据注入失败:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  seedMaintenanceData()
    .then(() => {
      console.log('脚本执行完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('脚本执行失败:', error)
      process.exit(1)
    })
}

module.exports = { seedMaintenanceData }
