{"name": "wechat-registration-backend", "version": "1.0.0", "description": "微信生态应用备案人员信息管理系统后端", "main": "dist/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["wechat", "registration", "backend", "express", "typescript"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^17.2.1", "exceljs": "^4.4.0", "express": "^5.1.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "mysql2": "^3.14.3", "node-cron": "^4.2.1", "sequelize": "^6.37.7", "sqlite3": "^5.1.7", "xlsx": "^0.18.5", "xss": "^1.0.15"}}