<!-- 开发作者: 学文 -->
<!-- 开发时间: 2025-08-21 -->
<!-- 模块说明: 设备维修记录表单页面（新增/编辑） -->

<template>
  <div class="device-maintenance-form">
    <div class="page-header">
      <div class="header-left">
        <h1>{{ isEdit ? '编辑维修记录' : '新增维修记录' }}</h1>
        <p>{{ isEdit ? '修改设备维修记录信息' : '创建新的设备维修记录' }}</p>
      </div>
      <div class="header-right">
        <el-button @click="handleBack">
          <el-icon><ArrowLeft /></el-icon>
          返回
        </el-button>
      </div>
    </div>

    <el-card v-loading="loading" class="form-card">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
        size="default"
      >
        <!-- 设备信息 -->
        <div class="form-section">
          <h3>设备信息</h3>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="设备" prop="deviceId" required>
                <el-select
                  v-model="formData.deviceId"
                  placeholder="请选择设备"
                  filterable
                  remote
                  :remote-method="searchDevices"
                  :loading="deviceLoading"
                  style="width: 100%"
                >
                  <el-option
                    v-for="device in deviceOptions"
                    :key="device.id"
                    :label="`${device.deviceCode} - ${device.model} (${device.brand})`"
                    :value="device.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="维修类型" prop="maintenanceType">
                <el-radio-group v-model="formData.maintenanceType">
                  <el-radio value="warranty">保修</el-radio>
                  <el-radio value="paid">付费</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 故障信息 -->
        <div class="form-section">
          <h3>故障信息</h3>
          <el-form-item label="故障描述" prop="faultDescription" required>
            <el-input
              v-model="formData.faultDescription"
              type="textarea"
              :rows="4"
              placeholder="请详细描述设备故障情况"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 维修信息 -->
        <div class="form-section">
          <h3>维修信息</h3>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="维修服务商" prop="serviceProvider">
                <el-input
                  v-model="formData.serviceProvider"
                  placeholder="请输入维修服务商名称"
                  maxlength="200"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="维修技术员" prop="technician">
                <el-input
                  v-model="formData.technician"
                  placeholder="请输入维修技术员姓名"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="开始时间" prop="startTime" required>
                <el-date-picker
                  v-model="formData.startTime"
                  type="datetime"
                  placeholder="请选择维修开始时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DDTHH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="完成时间" prop="endTime">
                <el-date-picker
                  v-model="formData.endTime"
                  type="datetime"
                  placeholder="请选择维修完成时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DDTHH:mm:ss"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-form-item label="维修费用" prop="maintenanceCost">
                <el-input-number
                  v-model="formData.maintenanceCost"
                  :min="0"
                  :precision="2"
                  placeholder="请输入维修费用"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="维修状态" prop="maintenanceStatus">
                <el-select v-model="formData.maintenanceStatus" placeholder="请选择维修状态" style="width: 100%">
                  <el-option label="维修中" value="in_progress" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="维修失败" value="failed" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 维修结果 -->
        <div class="form-section">
          <h3>维修结果</h3>
          <el-form-item label="维修结果" prop="maintenanceResult">
            <el-input
              v-model="formData.maintenanceResult"
              type="textarea"
              :rows="4"
              placeholder="请描述维修结果和处理情况"
              maxlength="1000"
              show-word-limit
            />
          </el-form-item>
        </div>

        <!-- 操作按钮 -->
        <div class="form-actions">
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新记录' : '创建记录' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { DeviceMaintenanceApi, type CreateMaintenanceRecordData, type UpdateMaintenanceRecordData } from '@/api/deviceMaintenance'
import { DeviceApi, type MobileDevice } from '@/api/device'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const deviceLoading = ref(false)
const formRef = ref<FormInstance>()
const deviceOptions = ref<MobileDevice[]>([])

// 判断是否为编辑模式
const isEdit = computed(() => route.name === 'DeviceMaintenanceEdit')

// 表单数据
const formData = reactive<CreateMaintenanceRecordData & { endTime?: string }>({
  deviceId: 0,
  faultDescription: '',
  maintenanceType: 'warranty',
  startTime: '',
  endTime: '',
  maintenanceCost: 0,
  serviceProvider: '',
  technician: '',
  maintenanceStatus: 'in_progress',
  maintenanceResult: ''
})

// 表单验证规则
const formRules: FormRules = {
  deviceId: [
    { required: true, message: '请选择设备', trigger: 'change' }
  ],
  faultDescription: [
    { required: true, message: '请输入故障描述', trigger: 'blur' },
    { min: 1, max: 1000, message: '故障描述长度应在1-1000字符之间', trigger: 'blur' }
  ],
  startTime: [
    { required: true, message: '请选择维修开始时间', trigger: 'change' }
  ],
  serviceProvider: [
    { max: 200, message: '维修服务商名称长度不能超过200字符', trigger: 'blur' }
  ],
  technician: [
    { max: 100, message: '维修技术员姓名长度不能超过100字符', trigger: 'blur' }
  ],
  maintenanceResult: [
    { max: 1000, message: '维修结果长度不能超过1000字符', trigger: 'blur' }
  ]
}

// 搜索设备
const searchDevices = async (query: string) => {
  if (!query) return
  
  try {
    deviceLoading.value = true
    const response = await DeviceApi.getDevices({ search: query, limit: 20 })
    
    if (response.success) {
      deviceOptions.value = response.data.devices
    }
  } catch (error) {
    console.error('搜索设备失败:', error)
  } finally {
    deviceLoading.value = false
  }
}

// 获取维修记录详情（编辑模式）
const fetchDetail = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const id = Number(route.params.id)
    
    if (!id) {
      ElMessage.error('无效的记录ID')
      router.push('/device-maintenance')
      return
    }

    const response = await DeviceMaintenanceApi.getMaintenanceRecordById(id)
    
    if (response.success) {
      const record = response.data
      Object.assign(formData, {
        deviceId: record.deviceId,
        faultDescription: record.faultDescription,
        maintenanceType: record.maintenanceType,
        startTime: record.startTime,
        endTime: record.endTime || '',
        maintenanceCost: record.maintenanceCost || 0,
        serviceProvider: record.serviceProvider || '',
        technician: record.technician || '',
        maintenanceStatus: record.maintenanceStatus,
        maintenanceResult: record.maintenanceResult || ''
      })
      
      // 如果有设备信息，添加到选项中
      if (record.device) {
        deviceOptions.value = [record.device]
      }
    } else {
      ElMessage.error(response.message || '获取详情失败')
      router.push('/device-maintenance')
    }
  } catch (error) {
    console.error('获取维修记录详情失败:', error)
    ElMessage.error('获取详情失败')
    router.push('/device-maintenance')
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    const submitData = { ...formData }
    
    // 清理空值
    if (!submitData.endTime) delete submitData.endTime
    if (!submitData.serviceProvider) delete submitData.serviceProvider
    if (!submitData.technician) delete submitData.technician
    if (!submitData.maintenanceResult) delete submitData.maintenanceResult
    
    let response
    if (isEdit.value) {
      const id = Number(route.params.id)
      response = await DeviceMaintenanceApi.updateMaintenanceRecord(id, submitData as UpdateMaintenanceRecordData)
    } else {
      response = await DeviceMaintenanceApi.createMaintenanceRecord(submitData as CreateMaintenanceRecordData)
    }
    
    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      router.push('/device-maintenance')
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
}

// 返回列表
const handleBack = () => {
  router.push('/device-maintenance')
}

// 组件挂载时初始化
onMounted(() => {
  fetchDetail()
  // 初始加载一些设备选项
  searchDevices('')
})
</script>

<style scoped>
.device-maintenance-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-card {
  min-height: 400px;
}

.form-section {
  margin-bottom: 32px;
}

.form-section h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #e4e7ed;
  padding-bottom: 8px;
}

.form-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e4e7ed;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 8px;
}
</style>
