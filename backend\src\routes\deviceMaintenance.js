// 开发作者: 学文
// 开发时间: 2025-08-20
// 模块说明: 设备维修记录路由配置

const express = require('express')
const router = express.Router()
const DeviceMaintenanceController = require('../controllers/DeviceMaintenanceController')
const { authMiddleware } = require('../middleware/auth')
const {
  createMaintenanceRecordValidation,
  updateMaintenanceRecordValidation,
  queryMaintenanceRecordValidation,
  idParamValidation
} = require('../validation/deviceMaintenanceValidation')

// 创建控制器实例
const maintenanceController = new DeviceMaintenanceController()

// 获取维修记录列表
router.get('/', 
  authMiddleware, 
  queryMaintenanceRecordValidation,
  maintenanceController.getMaintenanceRecordList
)

// 获取单个维修记录详情
router.get('/:id', 
  authMiddleware, 
  idParamValidation,
  maintenanceController.getMaintenanceRecordById
)

// 创建维修记录
router.post('/', 
  authMiddleware, 
  createMaintenanceRecordValidation,
  maintenanceController.createMaintenanceRecord
)

// 更新维修记录
router.put('/:id', 
  authMiddleware, 
  updateMaintenanceRecordValidation,
  maintenanceController.updateMaintenanceRecord
)

// 删除维修记录
router.delete('/:id',
  authMiddleware,
  idParamValidation,
  maintenanceController.deleteMaintenanceRecord
)

// 导出维修记录数据
router.get('/export/excel',
  authMiddleware,
  queryMaintenanceRecordValidation,
  maintenanceController.export
)

module.exports = router
