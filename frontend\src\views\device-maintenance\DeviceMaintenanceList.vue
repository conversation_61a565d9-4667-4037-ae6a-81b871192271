<!-- 开发作者: 学文 -->
<!-- 开发时间: 2025-01-20 -->
<!-- 模块说明: 设备维修记录页面（PC端） -->

<template>
  <div class="device-maintenance-list">
    <div class="page-header">
      <div class="header-left">
        <h1>维修记录</h1>
        <p>管理设备维修记录，包括故障描述、维修费用、维修状态等</p>
      </div>
      <div class="header-right">
        <el-button type="primary" @click="handleCreate">
          <el-icon><Plus /></el-icon>
          新增维修记录
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <el-card class="search-card">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.search"
            placeholder="请输入设备编号或故障描述"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="维修商">
          <el-input
            v-model="searchForm.serviceProvider"
            placeholder="请输入维修商"
            clearable
            style="width: 150px"
          />
        </el-form-item>
        <el-form-item label="维修类型">
          <el-select v-model="searchForm.maintenanceType" placeholder="请选择类型" clearable style="width: 120px">
            <el-option label="保修" value="warranty" />
            <el-option label="付费" value="paid" />
          </el-select>
        </el-form-item>
        <el-form-item label="维修状态">
          <el-select v-model="searchForm.maintenanceStatus" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="维修中" value="in_progress" />
            <el-option label="已完成" value="completed" />
            <el-option label="维修失败" value="failed" />
          </el-select>
        </el-form-item>
        <el-form-item label="维修日期">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <div class="table-header">
        <div class="table-title">
          <span>维修记录列表</span>
          <el-tag type="info">共 {{ pagination.total }} 条记录</el-tag>
        </div>
        <div class="table-stats">
          <el-statistic
            title="总维修费用"
            :value="totalCost"
            prefix="¥"
            :precision="2"
          />
          <el-statistic title="保修次数" :value="warrantyCount" />
          <el-statistic title="付费次数" :value="paidCount" />
        </div>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        height="600"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="device.deviceCode" label="设备编号" width="120" fixed="left" />
        <el-table-column prop="device.model" label="设备型号" width="150" show-overflow-tooltip />
        <el-table-column prop="faultDescription" label="故障描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="maintenanceType" label="维修类型" width="100">
          <template #default="{ row }">
            <el-tag :type="row.maintenanceType === 'warranty' ? 'success' : 'warning'" size="small">
              {{ row.maintenanceType === 'warranty' ? '保修' : '付费' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="startTime" label="开始时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.startTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="endTime" label="完成时间" width="160">
          <template #default="{ row }">
            <span v-if="row.endTime">{{ formatDate(row.endTime) }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="maintenanceCost" label="维修费用" width="120">
          <template #default="{ row }">
            <span v-if="row.maintenanceCost > 0" class="cost-text">¥{{ row.maintenanceCost }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="serviceProvider" label="维修商" width="150" show-overflow-tooltip />
        <el-table-column prop="technician" label="维修人员" width="100" />
        <el-table-column prop="maintenanceStatus" label="维修状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getMaintenanceStatusType(row.maintenanceStatus)" size="small">
              {{ getMaintenanceStatusText(row.maintenanceStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleView(row)">查看</el-button>
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button 
              v-if="row.maintenanceStatus === 'in_progress'" 
              type="success" 
              link 
              @click="handleComplete(row)"
            >
              完成
            </el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.limit"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 完成维修对话框 -->
    <el-dialog v-model="completeDialogVisible" title="完成维修" width="600px">
      <el-form :model="completeForm" label-width="100px">
        <el-form-item label="维修结果">
          <el-radio-group v-model="completeForm.status">
            <el-radio value="completed">维修成功</el-radio>
            <el-radio value="failed">维修失败</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="维修费用">
          <el-input-number 
            v-model="completeForm.cost" 
            :min="0"
            :precision="2"
            placeholder="请输入维修费用"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="维修结果">
          <el-input
            v-model="completeForm.result"
            type="textarea"
            :rows="4"
            placeholder="请输入维修结果描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="completeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirmComplete" :loading="completeLoading">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Download } from '@element-plus/icons-vue'
import { DeviceMaintenanceApi, type DeviceMaintenanceRecord, type MaintenanceRecordQuery } from '@/api/deviceMaintenance'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const tableData = ref<DeviceMaintenanceRecord[]>([])
const selectedRows = ref<DeviceMaintenanceRecord[]>([])

// 完成维修对话框
const completeDialogVisible = ref(false)
const completeLoading = ref(false)
const completeForm = reactive({
  id: 0,
  status: 'completed',
  cost: 0,
  result: ''
})

// 搜索表单
const searchForm = reactive<MaintenanceRecordQuery>({
  search: '',
  serviceProvider: '',
  maintenanceType: '',
  maintenanceStatus: '',
  dateRange: ''
})

// 分页数据
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 计算统计数据
const totalCost = computed(() => {
  return tableData.value.reduce((sum, item) => {
    const cost = parseFloat(item.maintenanceCost) || 0
    return sum + cost
  }, 0)
})

const warrantyCount = computed(() => {
  return tableData.value.filter(item => item.maintenanceType === 'warranty').length
})

const paidCount = computed(() => {
  return tableData.value.filter(item => item.maintenanceType === 'paid').length
})

// 获取维修记录数据
const fetchData = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params: MaintenanceRecordQuery = {
      page: pagination.page,
      limit: pagination.limit,
      ...searchForm
    }

    // 处理日期范围
    if (searchForm.dateRange && Array.isArray(searchForm.dateRange) && searchForm.dateRange.length === 2) {
      params.dateRange = `${searchForm.dateRange[0]},${searchForm.dateRange[1]}`
    }

    const response = await DeviceMaintenanceApi.getMaintenanceRecords(params)

    if (response.success) {
      tableData.value = response.data.maintenanceRecords
      pagination.total = response.data.pagination.total
    } else {
      ElMessage.error(response.message || '获取数据失败')
    }
  } catch (error) {
    console.error('获取维修记录失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    serviceProvider: '',
    maintenanceType: '',
    maintenanceStatus: '',
    dateRange: ''
  })
  pagination.page = 1
  fetchData()
}

// 新增
const handleCreate = () => {
  router.push('/device-maintenance/create')
}

// 查看详情
const handleView = (row: DeviceMaintenanceRecord) => {
  router.push(`/device-maintenance/${row.id}`)
}

// 编辑
const handleEdit = (row: DeviceMaintenanceRecord) => {
  router.push(`/device-maintenance/${row.id}/edit`)
}

// 完成维修
const handleComplete = (row: DeviceMaintenanceRecord) => {
  completeForm.id = row.id
  completeForm.status = 'completed'
  completeForm.cost = row.maintenanceCost || 0
  completeForm.result = ''
  completeDialogVisible.value = true
}

// 确认完成维修
const handleConfirmComplete = async () => {
  try {
    completeLoading.value = true

    const updateData = {
      maintenanceStatus: 'completed' as const,
      maintenanceCost: completeForm.cost,
      maintenanceResult: completeForm.result,
      endTime: new Date().toISOString()
    }

    const response = await DeviceMaintenanceApi.updateMaintenanceRecord(completeForm.id, updateData)

    if (response.success) {
      ElMessage.success('维修完成')
      completeDialogVisible.value = false
      fetchData()
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('完成维修失败:', error)
    ElMessage.error('操作失败')
  } finally {
    completeLoading.value = false
  }
}

// 删除
const handleDelete = async (row: DeviceMaintenanceRecord) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备"${row.device?.deviceCode || row.deviceId}"的维修记录吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await DeviceMaintenanceApi.deleteMaintenanceRecord(row.id)

    if (response.success) {
      ElMessage.success('删除成功')
      fetchData()
    } else {
      ElMessage.error(response.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除维修记录失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 导出
const handleExport = async () => {
  try {
    ElMessage.info('正在导出数据，请稍候...')

    const params = { ...searchForm }
    if (searchForm.dateRange && Array.isArray(searchForm.dateRange) && searchForm.dateRange.length === 2) {
      params.dateRange = `${searchForm.dateRange[0]},${searchForm.dateRange[1]}`
    }

    const blob = await DeviceMaintenanceApi.exportMaintenanceRecords(params)

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `维修记录_${new Date().toISOString().split('T')[0]}.xlsx`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  }
}

// 选择变化
const handleSelectionChange = (selection: DeviceMaintenanceRecord[]) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.limit = size
  pagination.page = 1
  fetchData()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
  pagination.page = page
  fetchData()
}

// 获取维修状态类型和文本
const getMaintenanceStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    in_progress: 'warning',
    completed: 'success',
    failed: 'danger'
  }
  return statusMap[status] || 'info'
}

const getMaintenanceStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    in_progress: '维修中',
    completed: '已完成',
    failed: '维修失败'
  }
  return statusMap[status] || status
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.device-maintenance-list {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
}

.table-stats {
  display: flex;
  gap: 20px;
}

.cost-text {
  color: #f56c6c;
  font-weight: 600;
}

.pagination-wrapper {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
