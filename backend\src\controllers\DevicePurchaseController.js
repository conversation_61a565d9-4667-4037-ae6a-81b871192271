// 开发作者: 学文
// 开发时间: 2025-08-20
// 模块说明: 设备采购记录管理控制器

const { DevicePurchaseRecord, MobileDevice, WechatEntity } = require('../models')
const { validationResult } = require('express-validator')
const { Op } = require('sequelize')
const xss = require('xss')

class DevicePurchaseController {
  // XSS防护函数
  static sanitizeInput(input) {
    if (typeof input === 'string') {
      return xss(input, {
        whiteList: {},
        stripIgnoreTag: true,
        stripIgnoreTagBody: ['script']
      })
    }
    return input
  }

  // 获取采购记录列表
  async getPurchaseRecordList(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { page = 1, limit = 10, search, deviceId, supplier, acceptanceStatus, dateRange } = req.query
      const offset = (page - 1) * limit

      // XSS防护
      const sanitizedSearch = DevicePurchaseController.sanitizeInput(search)
      const sanitizedSupplier = DevicePurchaseController.sanitizeInput(supplier)

      const whereClause = {}
      const includeClause = [
        {
          model: MobileDevice,
          as: 'device',
          attributes: ['id', 'deviceCode', 'model', 'brand'],
          include: [
            {
              model: WechatEntity,
              as: 'wechatEntity',
              attributes: ['id', 'name', 'entityType']
            }
          ]
        }
      ]

      if (sanitizedSearch) {
        // 使用全局OR搜索，包含主表和关联表字段
        whereClause[Op.or] = [
          // 采购记录表字段
          { batchNumber: { [Op.like]: `%${sanitizedSearch}%` } },
          { purchaser: { [Op.like]: `%${sanitizedSearch}%` } },
          { invoiceNumber: { [Op.like]: `%${sanitizedSearch}%` } },
          { supplier: { [Op.like]: `%${sanitizedSearch}%` } },
          // 设备信息字段（通过子查询）
          {
            '$device.deviceCode$': { [Op.like]: `%${sanitizedSearch}%` }
          },
          {
            '$device.model$': { [Op.like]: `%${sanitizedSearch}%` }
          },
          {
            '$device.brand$': { [Op.like]: `%${sanitizedSearch}%` }
          }
        ]
      }

      if (deviceId) {
        whereClause.deviceId = deviceId
      }

      if (sanitizedSupplier) {
        whereClause.supplier = { [Op.like]: `%${sanitizedSupplier}%` }
      }

      if (acceptanceStatus) {
        whereClause.acceptanceStatus = acceptanceStatus
      }

      if (dateRange) {
        const [startDate, endDate] = dateRange.split(',')
        if (startDate && endDate) {
          whereClause.purchaseDate = {
            [Op.between]: [startDate, endDate]
          }
        }
      }

      const { count, rows } = await DevicePurchaseRecord.findAndCountAll({
        where: whereClause,
        include: includeClause,
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']]
      })

      res.json({
        success: true,
        data: {
          purchaseRecords: rows,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(count / limit)
          }
        }
      })

    } catch (error) {
      console.error('获取采购记录列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取采购记录列表失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 获取单个采购记录详情
  async getPurchaseRecordById(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { id } = req.params
      
      const purchaseRecord = await DevicePurchaseRecord.findByPk(id, {
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['id', 'deviceCode', 'model', 'brand', 'status'],
            include: [
              {
                model: WechatEntity,
                as: 'wechatEntity',
                attributes: ['id', 'name', 'entityType']
              }
            ]
          }
        ]
      })

      if (!purchaseRecord) {
        return res.status(404).json({
          success: false,
          message: '采购记录不存在'
        })
      }

      res.json({
        success: true,
        data: purchaseRecord
      })

    } catch (error) {
      console.error('获取采购记录详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取采购记录详情失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 创建采购记录
  async createPurchaseRecord(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      // XSS防护
      const sanitizedData = {}
      Object.keys(req.body).forEach(key => {
        sanitizedData[key] = DevicePurchaseController.sanitizeInput(req.body[key])
      })

      // 验证设备是否存在
      const device = await MobileDevice.findByPk(sanitizedData.deviceId)
      if (!device) {
        return res.status(400).json({
          success: false,
          message: '指定的设备不存在'
        })
      }

      const purchaseRecord = await DevicePurchaseRecord.create(sanitizedData)

      // 获取完整的采购记录信息返回
      const createdRecord = await DevicePurchaseRecord.findByPk(purchaseRecord.id, {
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['id', 'deviceCode', 'model', 'brand'],
            include: [
              {
                model: WechatEntity,
                as: 'wechatEntity',
                attributes: ['id', 'name', 'entityType']
              }
            ]
          }
        ]
      })

      res.status(201).json({
        success: true,
        message: '采购记录创建成功',
        data: createdRecord
      })

    } catch (error) {
      console.error('创建采购记录失败:', error)
      res.status(500).json({
        success: false,
        message: '创建采购记录失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 更新采购记录
  async updatePurchaseRecord(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { id } = req.params

      // XSS防护
      const sanitizedData = {}
      Object.keys(req.body).forEach(key => {
        sanitizedData[key] = DevicePurchaseController.sanitizeInput(req.body[key])
      })

      // 检查采购记录是否存在
      const purchaseRecord = await DevicePurchaseRecord.findByPk(id)
      if (!purchaseRecord) {
        return res.status(404).json({
          success: false,
          message: '采购记录不存在'
        })
      }

      // 如果更新设备ID，验证设备是否存在
      if (sanitizedData.deviceId && sanitizedData.deviceId !== purchaseRecord.deviceId) {
        const device = await MobileDevice.findByPk(sanitizedData.deviceId)
        if (!device) {
          return res.status(400).json({
            success: false,
            message: '指定的设备不存在'
          })
        }
      }

      await purchaseRecord.update(sanitizedData)

      // 获取更新后的完整信息
      const updatedRecord = await DevicePurchaseRecord.findByPk(id, {
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['id', 'deviceCode', 'model', 'brand'],
            include: [
              {
                model: WechatEntity,
                as: 'wechatEntity',
                attributes: ['id', 'name', 'entityType']
              }
            ]
          }
        ]
      })

      res.json({
        success: true,
        message: '采购记录更新成功',
        data: updatedRecord
      })

    } catch (error) {
      console.error('更新采购记录失败:', error)
      res.status(500).json({
        success: false,
        message: '更新采购记录失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 删除采购记录
  async deletePurchaseRecord(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { id } = req.params
      
      const purchaseRecord = await DevicePurchaseRecord.findByPk(id)
      if (!purchaseRecord) {
        return res.status(404).json({
          success: false,
          message: '采购记录不存在'
        })
      }

      await purchaseRecord.destroy()

      res.json({
        success: true,
        message: '采购记录删除成功'
      })

    } catch (error) {
      console.error('删除采购记录失败:', error)
      res.status(500).json({
        success: false,
        message: '删除采购记录失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }
}

module.exports = DevicePurchaseController
