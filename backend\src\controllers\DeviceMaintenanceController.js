// 开发作者: 学文
// 开发时间: 2025-08-20
// 模块说明: 设备维修记录管理控制器

const { DeviceMaintenanceRecord, MobileDevice, WechatEntity } = require('../models')
const { validationResult } = require('express-validator')
const { Op } = require('sequelize')
const xss = require('xss')
const XLSX = require('xlsx')

class DeviceMaintenanceController {
  // XSS防护函数
  static sanitizeInput(input) {
    if (typeof input === 'string') {
      return xss(input, {
        whiteList: {},
        stripIgnoreTag: true,
        stripIgnoreTagBody: ['script']
      })
    }
    return input
  }

  // 获取维修记录列表
  async getMaintenanceRecordList(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { page = 1, limit = 10, search, deviceId, serviceProvider, maintenanceType, maintenanceStatus, dateRange } = req.query
      const offset = (page - 1) * limit

      // XSS防护
      const sanitizedSearch = DeviceMaintenanceController.sanitizeInput(search)
      const sanitizedServiceProvider = DeviceMaintenanceController.sanitizeInput(serviceProvider)

      const whereClause = {}
      
      if (sanitizedSearch) {
        whereClause[Op.or] = [
          // 维修记录表字段
          { faultDescription: { [Op.like]: `%${sanitizedSearch}%` } },
          { technician: { [Op.like]: `%${sanitizedSearch}%` } },
          { maintenanceResult: { [Op.like]: `%${sanitizedSearch}%` } },
          { serviceProvider: { [Op.like]: `%${sanitizedSearch}%` } },
          // 设备信息字段（通过子查询）
          {
            '$device.deviceCode$': { [Op.like]: `%${sanitizedSearch}%` }
          },
          {
            '$device.model$': { [Op.like]: `%${sanitizedSearch}%` }
          },
          {
            '$device.brand$': { [Op.like]: `%${sanitizedSearch}%` }
          }
        ]
      }

      if (deviceId) {
        whereClause.deviceId = deviceId
      }

      if (sanitizedServiceProvider) {
        whereClause.serviceProvider = { [Op.like]: `%${sanitizedServiceProvider}%` }
      }

      if (maintenanceType) {
        whereClause.maintenanceType = maintenanceType
      }

      if (maintenanceStatus) {
        whereClause.maintenanceStatus = maintenanceStatus
      }

      if (dateRange) {
        const [startDate, endDate] = dateRange.split(',')
        if (startDate && endDate) {
          whereClause.startTime = {
            [Op.between]: [startDate, endDate]
          }
        }
      }

      const { count, rows } = await DeviceMaintenanceRecord.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['id', 'deviceCode', 'model', 'brand'],
            include: [
              {
                model: WechatEntity,
                as: 'wechatEntity',
                attributes: ['id', 'name', 'entityType']
              }
            ]
          }
        ],
        limit: parseInt(limit),
        offset: parseInt(offset),
        order: [['createdAt', 'DESC']]
      })

      res.json({
        success: true,
        data: {
          maintenanceRecords: rows,
          pagination: {
            total: count,
            page: parseInt(page),
            limit: parseInt(limit),
            totalPages: Math.ceil(count / limit)
          }
        }
      })

    } catch (error) {
      console.error('获取维修记录列表失败:', error)
      res.status(500).json({
        success: false,
        message: '获取维修记录列表失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 获取单个维修记录详情
  async getMaintenanceRecordById(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { id } = req.params
      
      const maintenanceRecord = await DeviceMaintenanceRecord.findByPk(id, {
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['id', 'deviceCode', 'model', 'brand', 'status'],
            include: [
              {
                model: WechatEntity,
                as: 'wechatEntity',
                attributes: ['id', 'name', 'entityType']
              }
            ]
          }
        ]
      })

      if (!maintenanceRecord) {
        return res.status(404).json({
          success: false,
          message: '维修记录不存在'
        })
      }

      res.json({
        success: true,
        data: maintenanceRecord
      })

    } catch (error) {
      console.error('获取维修记录详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取维修记录详情失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 创建维修记录
  async createMaintenanceRecord(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      // XSS防护
      const sanitizedData = {}
      Object.keys(req.body).forEach(key => {
        sanitizedData[key] = DeviceMaintenanceController.sanitizeInput(req.body[key])
      })

      // 验证设备是否存在
      const device = await MobileDevice.findByPk(sanitizedData.deviceId)
      if (!device) {
        return res.status(400).json({
          success: false,
          message: '指定的设备不存在'
        })
      }

      const maintenanceRecord = await DeviceMaintenanceRecord.create(sanitizedData)

      // 获取完整的维修记录信息返回
      const createdRecord = await DeviceMaintenanceRecord.findByPk(maintenanceRecord.id, {
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['id', 'deviceCode', 'model', 'brand'],
            include: [
              {
                model: WechatEntity,
                as: 'wechatEntity',
                attributes: ['id', 'name', 'entityType']
              }
            ]
          }
        ]
      })

      res.status(201).json({
        success: true,
        message: '维修记录创建成功',
        data: createdRecord
      })

    } catch (error) {
      console.error('创建维修记录失败:', error)
      res.status(500).json({
        success: false,
        message: '创建维修记录失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 更新维修记录
  async updateMaintenanceRecord(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { id } = req.params

      // XSS防护
      const sanitizedData = {}
      Object.keys(req.body).forEach(key => {
        sanitizedData[key] = DeviceMaintenanceController.sanitizeInput(req.body[key])
      })

      // 检查维修记录是否存在
      const maintenanceRecord = await DeviceMaintenanceRecord.findByPk(id)
      if (!maintenanceRecord) {
        return res.status(404).json({
          success: false,
          message: '维修记录不存在'
        })
      }

      // 如果更新设备ID，验证设备是否存在
      if (sanitizedData.deviceId && sanitizedData.deviceId !== maintenanceRecord.deviceId) {
        const device = await MobileDevice.findByPk(sanitizedData.deviceId)
        if (!device) {
          return res.status(400).json({
            success: false,
            message: '指定的设备不存在'
          })
        }
      }

      await maintenanceRecord.update(sanitizedData)

      // 获取更新后的完整信息
      const updatedRecord = await DeviceMaintenanceRecord.findByPk(id, {
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['id', 'deviceCode', 'model', 'brand'],
            include: [
              {
                model: WechatEntity,
                as: 'wechatEntity',
                attributes: ['id', 'name', 'entityType']
              }
            ]
          }
        ]
      })

      res.json({
        success: true,
        message: '维修记录更新成功',
        data: updatedRecord
      })

    } catch (error) {
      console.error('更新维修记录失败:', error)
      res.status(500).json({
        success: false,
        message: '更新维修记录失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 删除维修记录
  async deleteMaintenanceRecord(req, res) {
    try {
      // 参数验证
      const errors = validationResult(req)
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '参数验证失败',
          errors: errors.array()
        })
      }

      const { id } = req.params
      
      const maintenanceRecord = await DeviceMaintenanceRecord.findByPk(id)
      if (!maintenanceRecord) {
        return res.status(404).json({
          success: false,
          message: '维修记录不存在'
        })
      }

      await maintenanceRecord.destroy()

      res.json({
        success: true,
        message: '维修记录删除成功'
      })

    } catch (error) {
      console.error('删除维修记录失败:', error)
      res.status(500).json({
        success: false,
        message: '删除维修记录失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 导出维修记录数据
  async export(req, res) {
    try {
      const { keyword, repairShop, maintenanceType, status, startDate, endDate } = req.query

      // 构建查询条件
      const whereCondition = {}

      if (keyword) {
        whereCondition[Op.or] = [
          { '$MobileDevice.deviceCode$': { [Op.like]: `%${keyword}%` } },
          { '$MobileDevice.model$': { [Op.like]: `%${keyword}%` } },
          { faultDescription: { [Op.like]: `%${keyword}%` } }
        ]
      }

      if (repairShop) {
        whereCondition.repairShop = { [Op.like]: `%${repairShop}%` }
      }

      if (maintenanceType) {
        whereCondition.maintenanceType = maintenanceType
      }

      if (status) {
        whereCondition.status = status
      }

      if (startDate && endDate) {
        whereCondition.startTime = {
          [Op.between]: [new Date(startDate), new Date(endDate)]
        }
      }

      // 查询数据
      const records = await DeviceMaintenanceRecord.findAll({
        where: whereCondition,
        include: [
          {
            model: MobileDevice,
            as: 'device',
            attributes: ['deviceCode', 'model', 'brand']
          }
        ],
        order: [['createdAt', 'DESC']]
      })

      // 准备Excel数据
      const excelData = records.map(record => ({
        '设备编号': record.device?.deviceCode || '',
        '设备型号': record.device?.model || '',
        '设备品牌': record.device?.brand || '',
        '故障描述': record.faultDescription || '',
        '维修类型': record.maintenanceType === 'warranty' ? '保修' : '付费',
        '开始时间': record.startTime ? new Date(record.startTime).toLocaleString('zh-CN') : '',
        '完成时间': record.endTime ? new Date(record.endTime).toLocaleString('zh-CN') : '',
        '维修费用': record.maintenanceCost ? `¥${record.maintenanceCost}` : '',
        '维修商': record.serviceProvider || '',
        '维修人员': record.technician || '',
        '维修状态': DeviceMaintenanceController.getStatusText(record.maintenanceStatus),
        '维修结果': record.maintenanceResult || '',
        '创建时间': new Date(record.createdAt).toLocaleString('zh-CN')
      }))

      // 创建工作簿
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(excelData)

      // 设置列宽
      const colWidths = [
        { wch: 12 }, // 设备编号
        { wch: 15 }, // 设备型号
        { wch: 10 }, // 设备品牌
        { wch: 30 }, // 故障描述
        { wch: 8 },  // 维修类型
        { wch: 18 }, // 开始时间
        { wch: 18 }, // 完成时间
        { wch: 12 }, // 维修费用
        { wch: 20 }, // 维修商
        { wch: 12 }, // 维修人员
        { wch: 10 }, // 维修状态
        { wch: 30 }, // 维修结果
        { wch: 18 }  // 创建时间
      ]
      ws['!cols'] = colWidths

      // 添加工作表到工作簿
      XLSX.utils.book_append_sheet(wb, ws, '维修记录')

      // 生成Excel文件
      const buffer = XLSX.write(wb, { type: 'buffer', bookType: 'xlsx' })

      // 设置响应头
      const filename = `维修记录_${new Date().toISOString().slice(0, 10)}.xlsx`
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`)

      // 发送文件
      res.send(buffer)

    } catch (error) {
      console.error('导出维修记录失败:', error)
      res.status(500).json({
        success: false,
        message: '导出失败',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      })
    }
  }

  // 获取状态文本
  static getStatusText(status) {
    const statusMap = {
      'pending': '待维修',
      'in_progress': '维修中',
      'completed': '已完成',
      'failed': '维修失败'
    }
    return statusMap[status] || status
  }
}

module.exports = DeviceMaintenanceController
