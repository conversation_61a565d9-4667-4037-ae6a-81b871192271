// 开发作者: 学文
// 开发时间: 2025-08-21
// 模块说明: 设备维修记录API接口

import api from '@/utils/api'

// 设备维修记录接口
export interface DeviceMaintenanceRecord {
  id: number
  deviceId: number
  faultDescription: string
  maintenanceType: 'warranty' | 'paid'
  startTime: string
  endTime?: string
  maintenanceCost?: number
  serviceProvider?: string
  technician?: string
  maintenanceStatus: 'in_progress' | 'completed' | 'failed'
  maintenanceResult?: string
  createdAt: string
  updatedAt: string
  device?: {
    id: number
    deviceCode: string
    model: string
    brand: string
    wechatEntity?: {
      id: number
      name: string
      entityType: string
    }
  }
}

// 创建维修记录数据接口
export interface CreateMaintenanceRecordData {
  deviceId: number
  faultDescription: string
  maintenanceType?: 'warranty' | 'paid'
  startTime: string
  endTime?: string
  maintenanceCost?: number
  serviceProvider?: string
  technician?: string
  maintenanceStatus?: 'in_progress' | 'completed' | 'failed'
  maintenanceResult?: string
}

// 更新维修记录数据接口
export interface UpdateMaintenanceRecordData {
  deviceId?: number
  faultDescription?: string
  maintenanceType?: 'warranty' | 'paid'
  startTime?: string
  endTime?: string
  maintenanceCost?: number
  serviceProvider?: string
  technician?: string
  maintenanceStatus?: 'in_progress' | 'completed' | 'failed'
  maintenanceResult?: string
}

// 查询参数接口
export interface MaintenanceRecordQuery {
  page?: number
  limit?: number
  search?: string
  deviceId?: number
  serviceProvider?: string
  maintenanceType?: 'warranty' | 'paid' | ''
  maintenanceStatus?: 'in_progress' | 'completed' | 'failed' | ''
  dateRange?: string
}

// API响应接口
export interface MaintenanceRecordResponse {
  success: boolean
  message?: string
  data: DeviceMaintenanceRecord
}

export interface MaintenanceRecordListResponse {
  success: boolean
  message?: string
  data: {
    maintenanceRecords: DeviceMaintenanceRecord[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }
}

// 设备维修记录API类
export class DeviceMaintenanceApi {
  // 获取维修记录列表
  static async getMaintenanceRecords(params: MaintenanceRecordQuery = {}): Promise<MaintenanceRecordListResponse> {
    const response = await api.get('/device-maintenance', { params })
    return response.data
  }

  // 获取单个维修记录详情
  static async getMaintenanceRecordById(id: number): Promise<MaintenanceRecordResponse> {
    const response = await api.get(`/device-maintenance/${id}`)
    return response.data
  }

  // 创建维修记录
  static async createMaintenanceRecord(data: CreateMaintenanceRecordData): Promise<MaintenanceRecordResponse> {
    const response = await api.post('/device-maintenance', data)
    return response.data
  }

  // 更新维修记录
  static async updateMaintenanceRecord(id: number, data: UpdateMaintenanceRecordData): Promise<MaintenanceRecordResponse> {
    const response = await api.put(`/device-maintenance/${id}`, data)
    return response.data
  }

  // 删除维修记录
  static async deleteMaintenanceRecord(id: number): Promise<{ success: boolean; message?: string }> {
    const response = await api.delete(`/device-maintenance/${id}`)
    return response.data
  }

  // 导出维修记录数据
  static async exportMaintenanceRecords(params: MaintenanceRecordQuery = {}): Promise<Blob> {
    const response = await api.get('/device-maintenance/export/excel', {
      params,
      responseType: 'blob'
    })
    return response.data
  }
}

export default DeviceMaintenanceApi
