// 开发作者: 学文
// 开发时间: 2025-08-20
// 模块说明: 设备采购记录API接口

import api from '@/utils/api'

// 设备采购记录接口
export interface DevicePurchaseRecord {
  id: number
  deviceId: number
  purchaseDate: string
  purchasePrice?: number
  supplier?: string
  quantity?: number
  batchNumber?: string
  purchaser?: string
  invoiceNumber?: string
  acceptanceStatus: 'pending' | 'accepted' | 'rejected'
  acceptanceRemarks?: string
  createdAt: string
  updatedAt: string
  device?: {
    id: number
    deviceCode: string
    model: string
    brand: string
    wechatEntity?: {
      id: number
      name: string
      entityType: string
    }
  }
}

// 查询参数接口
export interface PurchaseRecordQuery {
  page?: number
  limit?: number
  search?: string
  deviceId?: number
  supplier?: string
  acceptanceStatus?: string
  dateRange?: string
}

// 分页响应接口
export interface PurchaseRecordListResponse {
  success: boolean
  data: {
    purchaseRecords: DevicePurchaseRecord[]
    pagination: {
      total: number
      page: number
      limit: number
      totalPages: number
    }
  }
  message?: string
}

// 单个记录响应接口
export interface PurchaseRecordResponse {
  success: boolean
  data: DevicePurchaseRecord
  message?: string
}

// 创建/更新采购记录数据接口
export interface CreatePurchaseRecordData {
  deviceId: number
  purchaseDate: string
  purchasePrice?: number
  supplier?: string
  quantity?: number
  batchNumber?: string
  purchaser?: string
  invoiceNumber?: string
  acceptanceStatus?: 'pending' | 'accepted' | 'rejected'
  acceptanceRemarks?: string
}

export interface UpdatePurchaseRecordData extends Partial<CreatePurchaseRecordData> {}

// 设备采购记录API类
export class DevicePurchaseApi {
  // 获取采购记录列表
  static async getPurchaseRecords(params: PurchaseRecordQuery = {}): Promise<PurchaseRecordListResponse> {
    const response = await api.get('/device-purchase', { params })
    return response.data
  }

  // 获取单个采购记录详情
  static async getPurchaseRecordById(id: number): Promise<PurchaseRecordResponse> {
    const response = await api.get(`/device-purchase/${id}`)
    return response.data
  }

  // 创建采购记录
  static async createPurchaseRecord(data: CreatePurchaseRecordData): Promise<PurchaseRecordResponse> {
    const response = await api.post('/device-purchase', data)
    return response.data
  }

  // 更新采购记录
  static async updatePurchaseRecord(id: number, data: UpdatePurchaseRecordData): Promise<PurchaseRecordResponse> {
    const response = await api.put(`/device-purchase/${id}`, data)
    return response.data
  }

  // 删除采购记录
  static async deletePurchaseRecord(id: number): Promise<{ success: boolean; message?: string }> {
    const response = await api.delete(`/device-purchase/${id}`)
    return response.data
  }
}

export default DevicePurchaseApi
