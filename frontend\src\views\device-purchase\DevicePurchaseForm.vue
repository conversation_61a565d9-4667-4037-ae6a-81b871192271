<!-- 开发作者: 学文 -->
<!-- 开发时间: 2025-08-20 -->
<!-- 模块说明: 设备采购记录表单页面 -->

<template>
  <div class="device-purchase-form">
    <div class="page-header">
      <div class="header-left">
        <h1>{{ isEdit ? '编辑采购记录' : '新增采购记录' }}</h1>
        <p>{{ isEdit ? '修改设备采购记录信息' : '添加新的设备采购记录' }}</p>
      </div>
      <div class="header-right">
        <el-button @click="handleBack">
          <el-icon><ArrowLeft /></el-icon>
          返回列表
        </el-button>
      </div>
    </div>

    <el-card class="form-card">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        v-loading="loading"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备" prop="deviceId" required>
              <el-select
                v-model="form.deviceId"
                placeholder="请选择设备"
                filterable
                style="width: 100%"
                @change="handleDeviceChange"
              >
                <el-option
                  v-for="device in deviceOptions"
                  :key="device.id"
                  :label="`${device.deviceCode} - ${device.brand} ${device.model}`"
                  :value="device.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购日期" prop="purchaseDate" required>
              <el-date-picker
                v-model="form.purchaseDate"
                type="date"
                placeholder="请选择采购日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购价格" prop="purchasePrice">
              <el-input-number
                v-model="form.purchasePrice"
                :min="0"
                :precision="2"
                placeholder="请输入采购价格"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购数量" prop="quantity">
              <el-input-number
                v-model="form.quantity"
                :min="1"
                placeholder="请输入采购数量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商" prop="supplier">
              <el-input
                v-model="form.supplier"
                placeholder="请输入供应商名称"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购人员" prop="purchaser">
              <el-input
                v-model="form.purchaser"
                placeholder="请输入采购人员"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="批次号" prop="batchNumber">
              <el-input
                v-model="form.batchNumber"
                placeholder="请输入采购批次号"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="发票号码" prop="invoiceNumber">
              <el-input
                v-model="form.invoiceNumber"
                placeholder="请输入发票号码"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="验收状态" prop="acceptanceStatus">
              <el-select v-model="form.acceptanceStatus" placeholder="请选择验收状态" style="width: 100%">
                <el-option label="待验收" value="pending" />
                <el-option label="已验收" value="accepted" />
                <el-option label="已拒收" value="rejected" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="验收备注" prop="acceptanceRemarks">
          <el-input
            v-model="form.acceptanceRemarks"
            type="textarea"
            :rows="4"
            placeholder="请输入验收备注"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="submitting">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import { DevicePurchaseApi, type CreatePurchaseRecordData } from '@/api/devicePurchase'
import { DeviceApi, type MobileDevice } from '@/api/device'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const deviceOptions = ref<MobileDevice[]>([])

// 判断是否为编辑模式
const isEdit = computed(() => route.params.id && route.params.id !== 'create')

// 表单数据
const form = reactive<CreatePurchaseRecordData>({
  deviceId: 0,
  purchaseDate: '',
  purchasePrice: undefined,
  supplier: '',
  quantity: 1,
  batchNumber: '',
  purchaser: '',
  invoiceNumber: '',
  acceptanceStatus: 'pending',
  acceptanceRemarks: ''
})

// 表单验证规则
const rules: FormRules = {
  deviceId: [
    { required: true, message: '请选择设备', trigger: 'change' }
  ],
  purchaseDate: [
    { required: true, message: '请选择采购日期', trigger: 'change' }
  ],
  purchasePrice: [
    { type: 'number', min: 0, message: '采购价格必须大于等于0', trigger: 'blur' }
  ],
  quantity: [
    { type: 'number', min: 1, message: '采购数量必须大于0', trigger: 'blur' }
  ],
  supplier: [
    { max: 200, message: '供应商名称长度不能超过200字符', trigger: 'blur' }
  ],
  purchaser: [
    { max: 100, message: '采购人员长度不能超过100字符', trigger: 'blur' }
  ],
  batchNumber: [
    { max: 100, message: '批次号长度不能超过100字符', trigger: 'blur' }
  ],
  invoiceNumber: [
    { max: 100, message: '发票号码长度不能超过100字符', trigger: 'blur' }
  ],
  acceptanceRemarks: [
    { max: 1000, message: '验收备注长度不能超过1000字符', trigger: 'blur' }
  ]
}

// 加载设备选项
const loadDeviceOptions = async () => {
  try {
    const response = await DeviceApi.getDevices({ limit: 100 })
    if (response.success) {
      deviceOptions.value = response.data.devices
    }
  } catch (error) {
    console.error('加载设备选项失败:', error)
  }
}

// 设备选择变化
const handleDeviceChange = (deviceId: number) => {
  const device = deviceOptions.value.find(d => d.id === deviceId)
  if (device) {
    // 可以根据设备信息自动填充一些字段
    console.log('选择的设备:', device)
  }
}

// 加载编辑数据
const loadEditData = async () => {
  if (!isEdit.value) return
  
  try {
    loading.value = true
    const id = Number(route.params.id)
    const response = await DevicePurchaseApi.getPurchaseRecordById(id)
    
    if (response.success) {
      const data = response.data
      Object.assign(form, {
        deviceId: data.deviceId,
        purchaseDate: data.purchaseDate,
        purchasePrice: data.purchasePrice,
        supplier: data.supplier || '',
        quantity: data.quantity || 1,
        batchNumber: data.batchNumber || '',
        purchaser: data.purchaser || '',
        invoiceNumber: data.invoiceNumber || '',
        acceptanceStatus: data.acceptanceStatus,
        acceptanceRemarks: data.acceptanceRemarks || ''
      })
    } else {
      ElMessage.error(response.message || '获取数据失败')
      handleBack()
    }
  } catch (error) {
    console.error('加载编辑数据失败:', error)
    ElMessage.error('获取数据失败')
    handleBack()
  } finally {
    loading.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    let response
    if (isEdit.value) {
      const id = Number(route.params.id)
      response = await DevicePurchaseApi.updatePurchaseRecord(id, form)
    } else {
      response = await DevicePurchaseApi.createPurchaseRecord(form)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      handleBack()
    } else {
      ElMessage.error(response.message || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    if (error !== false) {
      console.error('提交表单失败:', error)
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
}

// 返回列表
const handleBack = () => {
  router.push('/device-purchase')
}

// 组件挂载时初始化
onMounted(async () => {
  await loadDeviceOptions()
  if (isEdit.value) {
    await loadEditData()
  }
})
</script>

<style scoped>
.device-purchase-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.header-left p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.form-card {
  max-width: 800px;
}
</style>
