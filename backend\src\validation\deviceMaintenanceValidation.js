// 开发作者: 学文
// 开发时间: 2025-08-20
// 模块说明: 设备维修记录验证规则

const { body, query, param } = require('express-validator')
const xss = require('xss')

// XSS防护函数
const sanitizeInput = (value) => {
  if (typeof value === 'string') {
    return xss(value, {
      whiteList: {},
      stripIgnoreTag: true,
      stripIgnoreTagBody: ['script']
    })
  }
  return value
}

// 创建维修记录验证规则
const createMaintenanceRecordValidation = [
  body('deviceId')
    .isInt({ min: 1 })
    .withMessage('设备ID必须是大于0的整数'),
  
  body('faultDescription')
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('故障描述长度必须在1-1000字符之间')
    .customSanitizer(sanitizeInput),
  
  body('maintenanceType')
    .optional()
    .isIn(['warranty', 'paid'])
    .withMessage('维修类型必须是 warranty 或 paid'),
  
  body('startTime')
    .isISO8601({ strict: false })
    .withMessage('维修开始时间格式不正确')
    .toDate(),
  
  body('endTime')
    .optional()
    .isISO8601({ strict: false })
    .withMessage('维修结束时间格式不正确')
    .toDate(),
  
  body('maintenanceCost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('维修费用必须是大于等于0的数字'),
  
  body('serviceProvider')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('维修服务商名称长度不能超过200字符')
    .customSanitizer(sanitizeInput),
  
  body('technician')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('维修技术员长度不能超过100字符')
    .customSanitizer(sanitizeInput),
  
  body('maintenanceStatus')
    .optional()
    .isIn(['in_progress', 'completed', 'failed'])
    .withMessage('维修状态必须是 in_progress、completed 或 failed'),
  
  body('maintenanceResult')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('维修结果长度不能超过1000字符')
    .customSanitizer(sanitizeInput)
]

// 更新维修记录验证规则
const updateMaintenanceRecordValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('无效的维修记录ID'),
  
  body('deviceId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('设备ID必须是大于0的整数'),
  
  body('faultDescription')
    .optional()
    .trim()
    .isLength({ min: 1, max: 1000 })
    .withMessage('故障描述长度必须在1-1000字符之间')
    .customSanitizer(sanitizeInput),
  
  body('maintenanceType')
    .optional()
    .isIn(['warranty', 'paid'])
    .withMessage('维修类型必须是 warranty 或 paid'),
  
  body('startTime')
    .optional()
    .isISO8601({ strict: false })
    .withMessage('维修开始时间格式不正确')
    .toDate(),
  
  body('endTime')
    .optional()
    .isISO8601({ strict: false })
    .withMessage('维修结束时间格式不正确')
    .toDate(),
  
  body('maintenanceCost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('维修费用必须是大于等于0的数字'),
  
  body('serviceProvider')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('维修服务商名称长度不能超过200字符')
    .customSanitizer(sanitizeInput),
  
  body('technician')
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage('维修技术员长度不能超过100字符')
    .customSanitizer(sanitizeInput),
  
  body('maintenanceStatus')
    .optional()
    .isIn(['in_progress', 'completed', 'failed'])
    .withMessage('维修状态必须是 in_progress、completed 或 failed'),
  
  body('maintenanceResult')
    .optional()
    .trim()
    .isLength({ max: 1000 })
    .withMessage('维修结果长度不能超过1000字符')
    .customSanitizer(sanitizeInput)
]

// 查询参数验证
const queryMaintenanceRecordValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  
  query('search')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('搜索关键词长度不能超过200字符')
    .customSanitizer(sanitizeInput),
  
  query('deviceId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('设备ID必须是大于0的整数'),
  
  query('serviceProvider')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('维修服务商名称长度不能超过200字符')
    .customSanitizer(sanitizeInput),
  
  query('maintenanceType')
    .optional()
    .custom((value) => {
      if (!value || value === '') return true
      return ['warranty', 'paid'].includes(value)
    })
    .withMessage('维修类型必须是 warranty 或 paid'),
  
  query('maintenanceStatus')
    .optional()
    .custom((value) => {
      if (!value || value === '') return true
      return ['in_progress', 'completed', 'failed'].includes(value)
    })
    .withMessage('维修状态必须是 in_progress、completed 或 failed'),
  
  query('dateRange')
    .optional()
    .custom((value) => {
      if (!value) return true
      const dates = value.split(',')
      if (dates.length !== 2) return false
      const [startDate, endDate] = dates
      return /^\d{4}-\d{2}-\d{2}$/.test(startDate) && /^\d{4}-\d{2}-\d{2}$/.test(endDate)
    })
    .withMessage('日期范围格式不正确，应为 YYYY-MM-DD,YYYY-MM-DD')
]

// ID参数验证
const idParamValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('无效的维修记录ID')
]

module.exports = {
  createMaintenanceRecordValidation,
  updateMaintenanceRecordValidation,
  queryMaintenanceRecordValidation,
  idParamValidation
}
